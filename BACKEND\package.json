{"name": "backend", "version": "1.0.0", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.0", "fluento": "file:..", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "stream-chat": "^8.60.0"}, "devDependencies": {"nodemon": "^3.1.9"}}